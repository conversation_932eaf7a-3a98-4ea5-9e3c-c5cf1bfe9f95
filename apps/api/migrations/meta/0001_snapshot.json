{"id": "4c323a93-c2aa-413c-a3e2-0b6fafd64cfe", "prevId": "1d3a6bd5-9b00-48d6-afb9-7a4931240e8a", "version": "7", "dialect": "postgresql", "tables": {"public.tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}