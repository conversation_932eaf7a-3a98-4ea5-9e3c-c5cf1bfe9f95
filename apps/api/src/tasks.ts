import { Hono } from 'hono';
import type { ConfigVariables } from './config.js';
import type { DbVariables } from './database/middleware.js';
import * as schema from './database/schema.js';
import { v4 as uuidv4 } from 'uuid';
import { eq, isNull, sql } from 'drizzle-orm';
import { zValidator } from '@hono/zod-validator';
import z from 'zod';
import { loadDb } from './database/db.js';

const dueDateValidator = z.object({
  date: z.string().regex(/\d{4}-\d{2}-\d{2}/)
    .refine(v => {
      try {
        new Date(v);
        return true;
      } catch {
        return false;
      }
    }, 'Invalid date'),
  time: z.string().regex(/\d{2}:\d{2}(:\d{2})?/)
    .refine(v => {
      try {
        new Date(`1970-01-01 ${v}`);
        return true;
      } catch {
        return false;
      }
    }, 'Invalid time')
    .optional()
    .nullable(),
  timezone: z.string().refine(v => {
      try {
        new Intl.DateTimeFormat(undefined, { timeZone: v });
        return true;
      } catch {
        return false;
      }
    }, 'Invalid timezone'),
  offset: z.object({
    hours: z.number().gte(-14).lte(14),
    minutes: z.number().gte(0).lt(60)
  })
});

const app = new Hono<{ Variables: ConfigVariables & DbVariables }>()
  .get('/', async (c) => {
    const db = c.get('db');
    const tasks = await db
      .select()
      .from(schema.tasksTable)
      .where(isNull(schema.tasksTable.deletedAt))
      .orderBy(sql`${schema.tasksTable.createdAt} DESC`)
      .execute();

    return c.json({ tasks });
  })
  .post('/',
    zValidator(
      'json',
      z.object({
        title: z.string(),
        description: z.string().optional().nullable()
      })
    ),
    async (c) => {
      const db = c.get('db');
      const body = c.req.valid('json');

      const [task] = await db.insert(schema.tasksTable)
        .values({
          id: uuidv4(),
          title: body.title,
          description: body.description
        })
        .returning()
        .execute();

      return c.json({ task });
    }
  )
  .patch('/:id',
    zValidator(
      'json',
      z.object({
        completed: z.boolean()
      })
    ),
    async (c) => {
      const db = c.get('db');
      const body = c.req.valid('json');
      const taskId = c.req.param('id');

      const result = await db.update(schema.tasksTable)
        .set({
          completedAt: body.completed ? new Date() : null
        })
        .where(eq(schema.tasksTable.id, taskId))
        .returning()
        .execute();

      if (!result.length) {
        return c.json({ error: 'Task not found' }, 404);
      }

      return c.json({ task: result[0] });
    }
  )
  .put(
    '/:id',
    zValidator(
      'param',
      z.object({
        id: z.string().uuid()
      })
    ),
    zValidator(
      'json',
      z.object({
        title: z.string(),
        description: z.string().optional().nullable(),
        dueDate: dueDateValidator.optional().nullable()
      })
    ),
    async (c) => {
      const db = c.get('db');
      const taskId = c.req.valid('param').id;
      const body = c.req.valid('json');

      const result = await db.update(schema.tasksTable)
        .set({
          title: body.title,
          description: body.description,
          dueDate: body.dueDate
        })
        .where(eq(schema.tasksTable.id, taskId))
        .returning()
        .execute();

      if (!result.length) {
        return c.json({ error: 'Task not found' }, 404);
      }

      return c.json({ task: result[0] });
    }
  )
  .delete('/:id', async (c) => {
    const db = c.get('db');
    const taskId = c.req.param('id');

    const result = await db.update(schema.tasksTable)
      .set({
        deletedAt: new Date()
      })
      .where(eq(schema.tasksTable.id, taskId))
      .returning()
      .execute();

    if (!result.length) {
      return c.json({ error: 'Task not found' }, 404);
    }

    return c.json({ task: result[0] });
  })
  .put(
    '/:id/due',
    zValidator(
      'param',
      z.object({
        id: z.string().uuid()
      })
    ),
    zValidator('json', dueDateValidator),
    async (c) => {
      const db = c.get('db');
      const taskId = c.req.valid('param').id;
      const body = c.req.valid('json');

      const result = await db.update(schema.tasksTable)
        .set({
          dueDate: body
        })
        .where(eq(schema.tasksTable.id, taskId))
        .returning()
        .execute();

      if (!result.length) {
        return c.json({ error: 'Task not found' }, 404);
      }

      return c.json({ task: result[0] });
  })
  .delete(
    '/:id/due',
    zValidator(
      'param',
      z.object({
        id: z.string().uuid()
      })
    ),
    async (c) => {
      const db = c.get('db');
      const taskId = c.req.valid('param').id;

      const result = await db.update(schema.tasksTable)
        .set({
          dueDate: null
        })
        .where(eq(schema.tasksTable.id, taskId))
        .returning()
        .execute();

      if (!result.length) {
        return c.json({ error: 'Task not found' }, 404);
      }

      return c.json({ task: result[0] });
    }
  )


export default app;
