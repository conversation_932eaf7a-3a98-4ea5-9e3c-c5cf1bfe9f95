import { z } from 'zod';
import config from 'config';
import { createMiddleware } from 'hono/factory';

const databaseConfigSchema = z.object({
  host: z.string(),
  port: z.number(),
  user: z.string(),
  password: z.string(),
  database: z.string(),
  runMigrations: z.boolean().optional().default(false)
});

const configSchema = z.object({
  port: z.number(),
  database: databaseConfigSchema
});

export type AppConfig = z.infer<typeof configSchema>;
export type DbConfig = z.infer<typeof databaseConfigSchema>;

export const loadConfig = () => configSchema.parse(config.util.toObject());

export const loadDbConfig = () => databaseConfigSchema.parse(config.get('database'));

export type ConfigVariables = {
  config: AppConfig;
}

export const configMiddleware = (config: AppConfig) => createMiddleware(async (c, next) => {
  c.set('config', config);
  await next();
});

