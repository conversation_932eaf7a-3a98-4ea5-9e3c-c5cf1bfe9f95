import { serve } from '@hono/node-server'
import { Hono } from 'hono'
import { initDb, loadDb } from './database/db.js'
import { cors } from 'hono/cors';
import { configMiddleware, loadConfig } from './config.js'
import { dbMiddleware } from './database/middleware.js'
import tasksApp from './tasks.js';
import { getLifetimeToken } from './lifetime.js';

const config = loadConfig();
await initDb(config.database);
const db = loadDb(config.database);

const app = new Hono()

app.use('*', cors());
app.use('*', configMiddleware(config));
app.use('*', dbMiddleware(db));
app.route('/tasks', tasksApp);

app.get('/', (c) => {
  return c.text('Hello World!');
});

const processStoppingToken = getLifetimeToken();

const server = serve({
  fetch: app.fetch,
  port: config.port
}, (info) => {
  console.log(`Server is running on http://localhost:${info.port}`)
});

processStoppingToken.addEventListener('abort', () => {
  server.close();
});
