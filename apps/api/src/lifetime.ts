export const getLifetimeToken = () => {
  const processStoppingToken = new AbortController();

  process.on('SIGINT', () => {
    processStoppingToken.abort();
  });

  process.on('beforeExit', () => {
    processStoppingToken.abort();
  });

  process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    processStoppingToken.abort();
  });

  process.on('unhandledRejection', (reason) => {
    console.error('Unhandled Rejection:', reason);
    processStoppingToken.abort();
  });

  return processStoppingToken.signal;
}
