import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import * as schema from './schema.js';
import type { DbConfig } from '../config.js';
import { join } from 'path';

export const initDb = async (config: DbConfig) => {
  if (config.runMigrations) {
    const db = loadDb(config);
    const migrationsDir = join(process.cwd(), 'migrations');
    await migrate(db, { migrationsFolder: migrationsDir });
  }
}

export const loadDb = (config: DbConfig) => {
  const url = `postgres://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
  return drizzle(url, { schema });
}
