{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "dependencies": {"@primeuix/themes": "^1.1.2", "luxon": "^3.6.1", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.5", "vue": "^3.5.13"}, "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@types/luxon": "^3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}