import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import type { Task, TaskDueDate } from './types';

export const useTasksStore = defineStore('tasks', () => {
  const tasks = ref<Task[]>([]);
  const openTasks = computed(() => {
    return tasks.value.filter(task => !task.completedAt);
  });
  const completedTasks = computed(() => {
    return tasks.value.filter(task => task.completedAt);
  });

  const createTask = async (task: Pick<Task, 'title'>) => {
    const endpointUrl = new URL('/tasks', import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(task)
    });

    const data = await response.json() as { task: Task };
    // ordered by created at descending, so this should always be the first task
    tasks.value.unshift(data.task);
  };

  const fetchTasks = async () => {
    const endpointUrl = new URL('/tasks', import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl);

    const data = await response.json() as { tasks: Task[] };
    tasks.value = data.tasks;
  };

  const toggleTask = async (ev: { taskId: string, completed: boolean }) => {
    const endpointUrl = new URL(`/tasks/${ev.taskId}`, import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        completed: ev.completed
      })
    });

    const data = await response.json() as { task: Task };

    tasks.value = tasks.value.map(task => {
      if (task.id === data.task.id) {
        return data.task;
      }

      return task;
    });
  };

  const deleteTask = async (taskId: string) => {
    const endpointUrl = new URL(`/tasks/${taskId}`, import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl, {
      method: 'DELETE'
    });

    const data = await response.json() as { task: Task };

    tasks.value = tasks.value.filter(task => task.id !== data.task.id);
  };

  const setTaskDueDate = async (taskId: string, dueDate: TaskDueDate) => {
    const endpointUrl = new URL(`/tasks/${taskId}/due`, import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dueDate)
    });

    const data = await response.json() as { task: Task };

    tasks.value = tasks.value.map(task => {
      if (task.id === data.task.id) {
        return data.task;
      }

      return task;
    });
  }

  const deleteTaskDueDate = async (taskId: string) => {
    const endpointUrl = new URL(`/tasks/${taskId}/due`, import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl, {
      method: 'DELETE'
    });

    const data = await response.json() as { task: Task };

    tasks.value = tasks.value.map(task => {
      if (task.id === data.task.id) {
        return data.task;
      }

      return task;
    });
  }

  const updateTask = async (taskId: string, task: Pick<Task, 'title' | 'description' | 'dueDate'>) => {
    const endpointUrl = new URL(`/tasks/${taskId}`, import.meta.env.VITE_API_URL);
    const response = await fetch(endpointUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(task)
    });

    const data = await response.json() as { task: Task };

    tasks.value = tasks.value.map(task => {
      if (task.id === data.task.id) {
        return data.task;
      }

      return task;
    });
  }

  return {
    tasks,
    openTasks,
    completedTasks,
    createTask,
    fetchTasks,
    toggleTask,
    deleteTask,
    setTaskDueDate,
    deleteTaskDueDate,
    updateTask
  };
});
