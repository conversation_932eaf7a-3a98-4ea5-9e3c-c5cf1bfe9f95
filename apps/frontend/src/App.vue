<script setup lang="ts">
  // import TaskListComponent from './components/TaskList.vue';
  import TaskList from './tasks/TaskList.vue';

</script>

<template>
  <header class="fixed top-0 left-0 w-full h-[48px]">
    <div class="h-full py-0.5 px-4 flex flex-row justify-start items-center gap-4 bg-zinc-700 shadow-lg">
      <h1 class="text-2xl font-bold">Home Manager</h1>
    </div>
  </header>
  <main :class="$style.main" class="w-full h-full">
    <div class="h-full flex flex-col justify-start items-stretch gap-8">
      <TaskList />
    </div>
  </main>
</template>

<style module>
  .main {
    padding: calc(12 * var(--spacing) + 48px) 24px 12px 24px;
  }
</style>
