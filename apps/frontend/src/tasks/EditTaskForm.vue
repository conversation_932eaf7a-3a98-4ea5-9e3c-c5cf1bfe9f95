<script setup lang="ts">
  import { computed, ref } from 'vue';
  import Button from 'primevue/button';
  import Dialog from 'primevue/dialog';
  import InputText from 'primevue/inputtext';
  import Textarea from 'primevue/textarea';
  import DatePicker from 'primevue/datepicker';
  import type { Task } from '../types';
  import { toAPI, useDueDates } from './date-helpers';
import { useTasksStore } from '../store';

  const props = defineProps<{ task: Task }>();
  const store = useTasksStore();

  const taskTitle = ref(props.task.title);
  const taskDescription = ref(props.task.description || '');
  const taskDueDate = useDueDates(props.task);
  const dueTimeInput = ref(taskDueDate.value?.dueTimeJS || null);
  const dueDateInput = ref(taskDueDate.value?.dueDateJS || null);

  const closeDialog = () => {
    dialogVisible.value = false;
    taskTitle.value = props.task.title;
    taskDescription.value = props.task.description || '';
    dueTimeInput.value = taskDueDate.value?.dueTimeJS || null;
    dueDateInput.value = taskDueDate.value?.dueDateJS || null;
  };

  const dialogVisible = ref(false);

  const dueDateIsDirty = computed(() =>{
    const newDueDate = toAPI({
      dueDate: dueDateInput.value,
      dueTime: dueTimeInput.value
    });

    const dueDateIsDirty = !(!newDueDate?.date && !props.task.dueDate?.date) && newDueDate?.date !== props.task.dueDate?.date;
    const dueTimeIsDirty = !(!newDueDate?.time && !props.task.dueDate?.time) && newDueDate?.time !== props.task.dueDate?.time;

    return dueDateIsDirty || dueTimeIsDirty;
  })

  const formIsDirty = computed(() => {
    const titleIsDirty = taskTitle.value !== props.task.title;
    const descriptionIsDirty = taskDescription.value !== props.task.description;

    return titleIsDirty || descriptionIsDirty;
  });

  const submitButtonDisabled = computed(() => {
    return !formIsDirty.value && !dueDateIsDirty.value;
  });

  const saveTask = () => {
    const isDirty = formIsDirty.value || dueDateIsDirty.value;
    if (!isDirty) {
      return;
    }

    const newDueDate = toAPI({
      dueDate: dueDateInput.value,
      dueTime: dueTimeInput.value
    });

    store.updateTask(props.task.id, {
      title: taskTitle.value,
      description: taskDescription.value,
      dueDate: newDueDate
    });

    dialogVisible.value = false;
  };

  const cancelColor = computed(() => {
    return formIsDirty.value || dueDateIsDirty.value ? 'danger' : 'secondary';
  });

</script>

<template>
  <Button
    severity="secondary"
    variant="text"
    size="small"
    icon="pi pi-pencil"
    aria-label="Edit Task"
    @click="dialogVisible = true"
  />

  <Dialog v-model:visible="dialogVisible" header="Edit Task" :style="{ width: '40vw' }" :closable="false" :closeOnEscape="false">
    <form class="w-full flex flex-col justify-start items-stretch gap-4" @submit.prevent="saveTask">
      <InputText v-model="taskTitle" class="w-full" placeholder="Title" aria-label="Task Title" />
      <Textarea rows="5" v-model="taskDescription" class="w-full" placeholder="Description" aria-label="Task Description" />
      <!-- <InputText v-model="taskDescription" class="w-full" placeholder="Task Description" aria-label="Task Description" /> -->
      <div class="flex flex-row justify-start items-center gap-4">
        <DatePicker v-model="dueDateInput" class="w-full" placeholder="Due Date" aria-label="Due Date" />
        <DatePicker v-model="dueTimeInput" class="w-full" placeholder="Time" aria-label="Due Time" timeOnly hourFormat="12" />
      </div>
      <div class="flex flex-row justify-end items-center">
        <Button :severity="cancelColor" variant="text" size="small" label="Cancel" @click="closeDialog" />
        <Button :disabled="submitButtonDisabled" severity="secondary" variant="text" size="small" label="Save" type="submit" />
      </div>
    </form>
  </Dialog>
</template>
