<script setup lang="ts">
  import { computed, defineProps } from 'vue';
  import type { Task } from '../types';
  import { DateTime } from 'luxon';

  const toDateTime = (dueDate: Task['dueDate']) => {
    if (!dueDate) {
      return [null, null, null];
    }

    const parsedDueDate = DateTime.fromISO(dueDate.date, { zone: dueDate.timezone });
    let parsedTime: DateTime | null = null;
    if (dueDate.time) {
      parsedTime = DateTime.fromISO(dueDate.time, { zone: dueDate.timezone });
    }

    const displayDateTime = parsedTime ?
      parsedDueDate.set({
        hour: parsedTime.hour,
        minute: parsedTime.minute
      }) :
      parsedDueDate.set({
        hour: 23,
        minute: 59
      });

    return [parsedDueDate, parsedTime, displayDateTime];
  }


  const props = defineProps<{
    dueDate: Task['dueDate']
  }>();

  const dueDateDisplay = computed(() => {
    if (!props.dueDate) {
      return null;
    }

    const [parsedDueDate, parsedTime] = toDateTime(props.dueDate);

    const parts = [
      parsedDueDate?.toLocaleString(DateTime.DATE_MED)
    ];

    if (parsedTime) {
      parts.push(parsedTime.toLocaleString(DateTime.TIME_SIMPLE));
    }

    return parts.join(' ');
  });

  const dueDateIso = computed(() => {
    if (!props.dueDate) {
      return null;
    }

    const [_, __, displayDateTime] = toDateTime(props.dueDate);
    if (!displayDateTime) {
      return null;
    }

    return displayDateTime.toISO();
  });

  const dueDateClass = computed(() => {
    if (!props.dueDate) {
      return 'dueDate';
    }

    const [_, __, displayDateTime] = toDateTime(props.dueDate);
    if (!displayDateTime) {
      return '';
    }

    const now = DateTime.local();

    if (displayDateTime.hasSame(now, 'day')) {
      return 'dueDateWarning';
    }

    if (displayDateTime < now) {
      return 'dueDateDanger';
    }

    return 'dueDate';
  });

</script>

<template>
  <time class="text-xs" :class="$style[dueDateClass]" :datetime="dueDateIso!" aria-label="Task due date">{{ dueDateDisplay }}</time>
</template>

<style module>
  .dueDate {
    color: var(--color-green-300);
  }

  .dueDateWarning {
    color: var(--color-orange-300);
  }

  .dueDateDanger {
    color: var(--color-red-300);
  }
</style>
