<script setup lang="ts">
  import { defineProps, computed } from 'vue';
  import type { Task } from '../types';
  import { useTasksStore } from '../store';

  const store = useTasksStore();

  const props = defineProps<{
    task: Task
  }>();

  const taskCompleted = computed(() => {
    return props.task.completedAt !== null;
  });

  const toggleTask = async () => {
    await store.toggleTask({
      taskId: props.task.id,
      completed: !taskCompleted.value
    });
  };

</script>

<template>
  <label class="group flex items-center cursor-pointer relative">
      <input
        type="checkbox"
        :checked="taskCompleted"
        @change="toggleTask"
        class="
          peer
          h-5
          w-5
          cursor-pointer
          transition-all
          appearance-none
          rounded-full
          shadow
          hover:shadow-md
          border
          dark:border-slate-300
          checked:dark:bg-cyan-800
          checked:dark:border-cyan-800
          group-hover:not-checked:bg-transparent
          group-hover:dark:bg-cyan-800
          group-hover:dark:border-cyan-800
        "
        id="check"
      />
      <!-- <span class="sr-only">{{ props.task.title }}</span> -->
      <span class="absolute text-white opacity-0 peer-checked:opacity-100 group-hover:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor"
          stroke="currentColor" stroke-width="1">
          <path fill-rule="evenodd"
          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
          clip-rule="evenodd"></path>
        </svg>
      </span>
  </label>
</template>
