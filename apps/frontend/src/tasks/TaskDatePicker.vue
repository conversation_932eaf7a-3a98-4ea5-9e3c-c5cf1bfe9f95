<script setup lang="ts">
  import { computed, defineProps, ref, watch } from 'vue';
  import type { Task, TaskDueDate } from '../types';
  import Button from 'primevue/button';
  import DatePicker from 'primevue/datepicker';
  import Popover from 'primevue/popover';
  import SplitButton from 'primevue/splitbutton';
  import Divider from 'primevue/divider';
  import { DateTime } from 'luxon';
  import { useTasksStore } from '../store';

  const store = useTasksStore();

  const props = defineProps<{
    task: Task
  }>();

  const dateInput = ref();
  const timeInput = ref();
  const dateInputMenu = ref();
  const showTimeInputManual = ref(false);

  if (props.task.dueDate) {
    dateInput.value = DateTime.fromISO(props.task.dueDate.date).toJSDate();
  }

  if (props.task.dueDate?.time) {
    timeInput.value = new Date(`1970-01-01 ${props.task.dueDate!.time}`);
  }

  const dt = DateTime.local();
  const taskDate = ref<Partial<TaskDueDate>>(props.task.dueDate || {
    timezone: dt.zoneName,
    offset: {
      hours: dt.offset / 60,
      minutes: dt.offset % 60
    }
  });

  const toggleMenu = (ev: MouseEvent) => {
    dateInputMenu.value?.toggle(ev);
  };

  const removeTime = () => {
    timeInput.value = null;
    showTimeInputManual.value = false;
  };

  watch(dateInput, (newDate) => {
    if (!newDate) {
      taskDate.value = {
        timezone: dt.zoneName,
        offset: {
          hours: dt.offset / 60,
          minutes: dt.offset % 60
        }
      }
      return;
    }

    taskDate.value = {
      ...(taskDate.value || {}),
      date: DateTime.fromJSDate(newDate)
        .toFormat('yyyy-MM-dd')
    }

    console.log(taskDate.value);
  });

  watch(timeInput, (newTime: Date) => {
    const currentValue = taskDate.value || {};

    if (!newTime) {
      taskDate.value = {
        ...currentValue,
        time: null
      }
      return;
    }

  });

  watch(taskDate, (newDate) => {
    console.log(newDate);
    if (!newDate?.date) {
      store.deleteTaskDueDate(props.task.id);
      return;
    }

    if (!newDate.date) {
      return;
    }

    store.setTaskDueDate(props.task.id, newDate as TaskDueDate);
  });

  const saveTime = () => {
    if (!timeInput.value) {
      return;
    }

    taskDate.value = {
      ...taskDate.value,
      time: DateTime.fromJSDate(timeInput.value)
        .toFormat('HH:mm:00')
    }
  }

  const removeDueDate = () => {
    removeTime();
    dateInput.value = null;
  };

  const showTimeInput = computed(() => {
    if (taskDate.value?.time) {
      return true;
    }

    return showTimeInputManual.value;
  });

</script>

<template>
  <Button variant="text" size="small" icon="pi pi-calendar" @click="toggleMenu" />
  <Popover ref="dateInputMenu">
    <div class="flex flex-col gap-4">
      <div class="flex flex-col justify-start items-stretch gap-2">
        <div class="flex flex-row justify-start items-center gap-4">
          <Button severity="secondary" variant="text" size="small" @click="removeDueDate" label="No due date" />
        </div>
        <DatePicker v-model="dateInput" inline />
        <Divider :pt="{
          root: 'm-0!'
        }" />
        <div v-if="showTimeInput" class="flex flex-col justify-start items-stretch gap-2">
          <Button severity="secondary" variant="text" size="small" @click="removeTime" label="Remove Time" />
          <DatePicker class="w-full" v-model="timeInput" timeOnly hourFormat="12" />
          <Button severity="secondary" variant="text" size="small" label="Save" @click="saveTime" />
        </div>
        <Button v-else severity="secondary" variant="text" size="small" @click="showTimeInputManual = !showTimeInputManual" label="Add Time" />
      </div>
    </div>
  </Popover>
</template>
