import { ref, type Ref } from 'vue';
import type { Task, TaskDueDate } from '../types';
import { DateTime } from 'luxon';
import { useTasksStore } from '../store';

const toDateTime = (dueDate: Task['dueDate']) => {
  if (!dueDate) {
    return [null, null, null];
  }

  const parsedDueDate = DateTime.fromISO(dueDate.date, { zone: dueDate.timezone });
  let parsedTime: DateTime | null = null;
  if (dueDate.time) {
    parsedTime = DateTime.fromISO(dueDate.time, { zone: dueDate.timezone });
  }

  const displayDateTime = parsedTime ?
    parsedDueDate.set({
      hour: parsedTime.hour,
      minute: parsedTime.minute
    }) :
    parsedDueDate.set({
      hour: 23,
      minute: 59
    });

  return [parsedDueDate, parsedTime, displayDateTime];
}

type DueDateRef = {
  dueDate: DateTime;
  dueTime: DateTime | null;
  displayDateTime: DateTime;
  dueDateJS: Date;
  dueTimeJS: Date | null;
}

export const useDueDates = (task: Task): Ref<DueDateRef | null> => {
  const [parsedDueDate, parsedTime, displayDateTime] = toDateTime(task.dueDate);
  if (!parsedDueDate) {
    return ref(null);
  }

  const dueDate: DueDateRef = {
    dueDate: parsedDueDate,
    dueTime: parsedTime,
    displayDateTime: displayDateTime!,
    dueDateJS: parsedDueDate.toJSDate(),
    dueTimeJS: parsedTime ? parsedTime.toJSDate() : null
  }

  return ref(dueDate);
}

export const toAPI = (opts: { dueDate: Date| null, dueTime: Date | null }): TaskDueDate | null => {
  if (!opts.dueDate) {
    return null;
  }

  const dueDate = DateTime.fromJSDate(opts.dueDate);
  const dueTime = opts.dueTime ? DateTime.fromJSDate(opts.dueTime) : null;

  return {
    date: dueDate.toISODate()!,
    time: dueTime ? dueTime.toFormat('HH:mm:00') : null,
    timezone: dueDate.zoneName!,
    offset: {
      hours: dueDate.offset / 60,
      minutes: dueDate.offset % 60
    }
  };
}
