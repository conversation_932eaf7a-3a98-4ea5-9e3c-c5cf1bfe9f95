<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import { useTasksStore } from '../store';
  import TaskListItem from './TaskListItem.vue';
  import InputText from 'primevue/inputtext';
  import Button from 'primevue/button';

  const taskStore = useTasksStore();

  onBeforeMount(() => {
    taskStore.fetchTasks();
  });

  const newTaskTitle = ref('');

  const createTask = async () => {
    if (!newTaskTitle.value) {
      return;
    }

    await taskStore.createTask({
      title: newTaskTitle.value
    });

    newTaskTitle.value = '';
  };

</script>

<template>
  <div class="h-full flex flex-col justify-start items-stretch gap-4 w-full max-w-[600px] mx-auto">
    <form class="flex flex-row justify-start items-center gap-4 w-full" @submit.prevent="createTask">
      <InputText v-model="newTaskTitle" class="w-full" placeholder="Add Task" aria-label="New Task Title" />
      <Button severity="secondary" variant="text" size="small" label="Add" />
    </form>
    <div class="overflow-auto">
      <ul class="px-1">
        <TaskListItem
          v-for="task in taskStore.tasks"
          :key="task.id"
          :task="task"
          :showDivider="taskStore.tasks.indexOf(task) !== taskStore.tasks.length - 1"
        />
      </ul>
    </div>
  </div>
</template>
