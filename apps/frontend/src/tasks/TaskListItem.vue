<script setup lang="ts">
  import type { Task } from '../types';
  import TaskCheckbox from './TaskCheckbox.vue';
  import TaskDueDate from './TaskDueDate.vue';
  import Button from 'primevue/button';
  import TaskDatePicker from './TaskDatePicker.vue';
  import EditTaskForm from './EditTaskForm.vue';
  import { useTasksStore } from '../store';

  const store = useTasksStore();

  const props = defineProps<{
    task: Task
    showDivider?: boolean
  }>();

  const deleteTask = () => store.deleteTask(props.task.id);

</script>

<template>
  <li class="flex flex-row justify-start items-stretch h-16">
    <div class="flex-grow flex flex-row justify-start items-stretch h-full gap-6">
      <TaskCheckbox :task="props.task" />
      <div class="flex flex-col justify-center items-stretch">
        <span class="font-bold">{{ props.task.title }}</span>
        <span class="text-sm text-slate-500">{{ props.task.description }}</span>
        <TaskDueDate :dueDate="props.task.dueDate" />
      </div>
    </div>
    <div class="flex flex-row justify-end items-center gap-1">
      <EditTaskForm :task="props.task" />
      <TaskDatePicker :task="props.task" />
      <Button
        severity="danger"
        variant="text"
        size="small"
        icon="pi pi-trash"
        aria-label="Delete Task"
        @click="deleteTask"
      />
    </div>
  </li>
</template>
